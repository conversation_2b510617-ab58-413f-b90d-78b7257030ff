{"tests/test_enhanced_architecture.py::TestFlowArchitecture::test_navigation_agent_initialization": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_extraction_agent_initialization": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_validation_agent_initialization": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_cyclical_processor_initialization": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_json_manager_session_creation": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_json_manager_product_update": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_backup_manager_creation": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_enhanced_progress_tracker": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_performance_metrics": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_message_protocol": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_feedback_analyzer": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_feedback_coordinator": true, "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_architecture_info": true}