["tests/test_enhanced_architecture.py::TestFlowArchitecture::test_architecture_info", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_backup_manager_creation", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_context_manager", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_cyclical_processor_initialization", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_enhanced_progress_tracker", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_extraction_agent_initialization", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_feedback_analyzer", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_feedback_coordinator", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_flow_plot_creation", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_flow_router", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_flow_scraper_initialization", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_flow_state_management", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_json_manager_product_update", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_json_manager_session_creation", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_message_protocol", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_navigation_agent_initialization", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_performance_metrics", "tests/test_enhanced_architecture.py::TestFlowArchitecture::test_validation_agent_initialization"]